// 设计系统 - 统一的样式变量和类
// 基于赛博朋克/未来科技风格，针对移动端优化

// ==================== 颜色系统 ====================
:root {
  // 主色调
  --primary-color: #00bcd4;
  --primary-color-light: rgba(0, 188, 212, 0.1);
  --primary-color-medium: rgba(0, 188, 212, 0.2);
  --primary-color-strong: rgba(0, 188, 212, 0.3);

  // 文本选中颜色
  --selection-bg: rgba(0, 188, 212, 0.3);
  --selection-color: #ffffff;
  
  // 强调色
  --accent-color: #00ffff;
  --accent-color-light: rgba(0, 255, 255, 0.1);
  --accent-color-medium: rgba(0, 255, 255, 0.2);
  --accent-color-strong: rgba(0, 255, 255, 0.3);
  
  // 文字颜色 (移动端优化 - 更高对比度)
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-tertiary: rgba(255, 255, 255, 0.8);
  --text-disabled: rgba(255, 255, 255, 0.6);
  
  // 背景色
  --bg-glass: rgba(30, 58, 138, 0.15);
  --bg-glass-hover: rgba(30, 58, 138, 0.25);
  --border-glass: rgba(255, 255, 255, 0.2);
  --border-accent: rgba(0, 255, 255, 0.3);
  
  // 阴影
  --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.2);
  --shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.3);
  --shadow-accent: 0 0 20px rgba(0, 255, 255, 0.2);

  // 字体大小 (移动端优化)
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 22px;
  --font-size-2xl: 26px;
  --font-size-3xl: 32px;

  // 间距系统
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;

  // 边框圆角
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 20px;
  --border-radius-full: 50px;

  // 其他颜色
  --text-quaternary: rgba(255, 255, 255, 0.5);
  --border-light: rgba(255, 255, 255, 0.1);
  --bg-hover: rgba(255, 255, 255, 0.05);
  --bg-input: rgba(255, 255, 255, 0.08);
  --bg-input-focus: rgba(255, 255, 255, 0.12);
  --overlay-dark: rgba(0, 0, 0, 0.8);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --success-color: #22c55e;
  --success-color-light: rgba(34, 197, 94, 0.1);
  --success-color-medium: rgba(34, 197, 94, 0.2);
  --success-color-strong: rgba(34, 197, 94, 0.3);
  --error-color: #ef4444;
  --error-color-light: rgba(239, 68, 68, 0.1);
  --error-color-medium: rgba(239, 68, 68, 0.2);
  --error-color-strong: rgba(239, 68, 68, 0.3);
}

// 全局文本选中样式
::selection {
  background-color: var(--selection-bg);
  color: var(--selection-color);
}

::-moz-selection {
  background-color: var(--selection-bg);
  color: var(--selection-color);
}

// ==================== 字体系统 (移动端优化) ====================

// 主标题 - 用于页面标题
.cyber-title {
  color: var(--text-primary);
  font-size: 42px;
  font-weight: 700;
  line-height: 1.4;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

// 副标题 - 用于页面副标题
.cyber-subtitle {
  color: var(--text-secondary);
  font-size: 42px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

// 功能标题 - 用于功能卡片标题
.cyber-feature-title {
  color: var(--text-primary);
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
}

// 功能描述 - 用于功能卡片描述
.cyber-feature-desc {
  color: var(--text-tertiary);
  font-size: 24px;
  font-weight: 400;
  line-height: 1.5;
}

// 正文 - 用于一般内容
.cyber-body {
  color: var(--text-secondary);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
}

// 辅助文字 - 用于次要信息
.cyber-caption {
  color: var(--text-tertiary);
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4;
}

// ==================== 组件系统 ====================

// 基础卡片
.cyber-card {
  background: var(--bg-glass);
  border: 2px solid var(--border-accent);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-strong), var(--shadow-accent);
  transition: all 0.3s ease;
  
  &:hover {
    background: var(--bg-glass-hover);
    transform: translateY(-2px);
  }
}

// 功能卡片
.cyber-feature-card {
  display: flex;
  align-items: center;
  gap: 20px;
  background: rgba(113, 229, 247, 0.15); // 比container更浅的透明度（从0.6改为0.3）
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 16px;
  padding: 18px; // 增加内边距
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 0 8px rgba(0, 255, 255, 0.2);
}

// 功能图标
.cyber-feature-icon {
  font-size: 36px;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--accent-color-light);
  border-radius: 50%;
  flex-shrink: 0;
}

// 弹窗/模态框
.cyber-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay-dark);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.cyber-modal-content {
  background: var(--bg-glass);
  border: 2px solid var(--border-accent);
  border-radius: 20px;
  padding: 30px;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-strong), var(--shadow-accent);
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  transition: all 0.3s ease;
}

// ==================== 按钮系统 ====================

// 基础按钮样式
.cyber-btn {
  padding: 18px 28px;
  border-radius: 20px;
  font-size: 24px; // 增加6px (18px -> 24px)
  font-weight: 600;
  cursor: pointer;
  border: 2px solid;
  background: transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-soft);
  }

  &:active {
    transform: translateY(0);
  }
}

// 主要按钮
.cyber-btn-primary {
  @extend .cyber-btn;
  color: var(--primary-color);
  border-color: var(--primary-color);
  background: var(--primary-color-light);
  
  &:hover {
    background: var(--primary-color-medium);
    box-shadow: 0 8px 24px var(--primary-color-strong);
  }
}

// 次要按钮
.cyber-btn-secondary {
  @extend .cyber-btn;
  color: var(--text-secondary);
  border-color: var(--border-glass);
  width: 250px;
  min-width: 200px; // 设置最小宽度让按钮更宽
}

// ==================== 工具类 ====================

// 文字阴影
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

// 居中
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 垂直居中
.flex-center-vertical {
  display: flex;
  align-items: center;
}

// 间距
.gap-small { gap: 8px; }
.gap-medium { gap: 16px; }
.gap-large { gap: 24px; }

// 边距
.mb-small { margin-bottom: 8px; }
.mb-medium { margin-bottom: 16px; }
.mb-large { margin-bottom: 24px; }
